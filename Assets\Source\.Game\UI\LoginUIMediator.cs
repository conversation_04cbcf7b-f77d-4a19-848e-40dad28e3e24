﻿using CLMM.Repository;
using TMPro;
using UnityEngine.UI;
using Universe;

namespace CLMM.UI
{
    [Component(typeof(IMediator), "LoginUI")]
    public class LoginUIMediator : Mediator
    {
        private readonly UserRepository _userRepository;

        private TMP_InputField _accountInput;
        private TMP_InputField _passwordInput;
        private Button _loginButton;

        public LoginUIMediator(UserRepository userRepository)
        {
            _userRepository = userRepository;
        }

        public override void OnCreate()
        {
            var vars = this.UI.GetVariables();
            _accountInput = vars.Get<TMP_InputField>("AccountInput");
            _passwordInput = vars.Get<TMP_InputField>("PasswordInput");
            _loginButton = vars.Get<Button>("LoginButton");

            _accountInput.onValueChanged.AddListener(OnAccountChanged);
            _passwordInput.onValueChanged.AddListener(OnPasswordChanged);
            _loginButton.interactable = false;
            _loginButton.onClick.AddListener(OnLoginClicked);

            _accountInput.text = "lib";
            _passwordInput.text = "123123";

            _userRepository.LoginState.Observe(this.UI, OnLoginStateChanged);
        }

        private void OnAccountChanged(string value) { }

        private void OnPasswordChanged(string value) { }

        private void OnLoginClicked()
        {
            _userRepository.Login(_accountInput.text, _passwordInput.text);
        }

        private void OnLoginStateChanged((int code, string message) state)
        {
            _loginButton.interactable = state.code <= 0;
        }
    }
}